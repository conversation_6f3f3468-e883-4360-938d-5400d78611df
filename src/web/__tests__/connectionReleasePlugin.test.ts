import { createY<PERSON>, Plug<PERSON> } from "graphql-yoga";
import { buildSchema } from "graphql";

// Mock connection and release function
const mockReleaseConnection = jest.fn();
const mockConnection = {};

// Simple test schema
const schema = buildSchema(`
  type Query {
    hello: String
    error: String
  }
`);

// Test resolvers
const resolvers = {
  Query: {
    hello: () => "Hello World!",
    error: () => {
      throw new Error("Test error");
    },
  },
};

// Connection release plugin (copied from dependencies.ts for testing)
const connectionReleasePlugin: Plugin<
  Record<string, unknown>,
  any,
  any
> = {
  onExecute() {
    return {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onExecuteDone(payload: any) {
        // Release connection after execution, regardless of success or error
        const context = payload.args.contextValue;

        if (context?.releaseConnection) {
          try {
            // If there was an error in execution, pass it to releaseConnection
            const error = payload.result.errors?.[0];
            context.releaseConnection(
              error ? new Error(error.message) : undefined,
            );
          } catch (releaseError) {
            // Log the error but don't throw to avoid masking the original error
            console.error("Error releasing database connection:", releaseError);
          }
        }
      },
    };
  },
};

describe("connectionReleasePlugin", () => {
  beforeEach(() => {
    mockReleaseConnection.mockClear();
  });

  it("should release connection on successful query", async () => {
    const yoga = createYoga({
      schema,
      resolvers,
      context: () => ({
        connection: mockConnection,
        releaseConnection: mockReleaseConnection,
      }),
      plugins: [connectionReleasePlugin],
    });

    const response = await yoga.fetch("http://localhost/graphql", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: "{ hello }",
      }),
    });

    expect(response.status).toBe(200);
    expect(mockReleaseConnection).toHaveBeenCalledTimes(1);
    expect(mockReleaseConnection).toHaveBeenCalledWith(undefined);
  });

  it("should release connection on query error", async () => {
    const yoga = createYoga({
      schema,
      resolvers,
      context: () => ({
        connection: mockConnection,
        releaseConnection: mockReleaseConnection,
      }),
      plugins: [connectionReleasePlugin],
    });

    const response = await yoga.fetch("http://localhost/graphql", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: "{ error }",
      }),
    });

    expect(response.status).toBe(200); // GraphQL errors still return 200
    expect(mockReleaseConnection).toHaveBeenCalledTimes(1);
    expect(mockReleaseConnection).toHaveBeenCalledWith(expect.any(Error));
  });

  it("should handle missing releaseConnection gracefully", async () => {
    const yoga = createYoga({
      schema,
      resolvers,
      context: () => ({
        connection: mockConnection,
        // No releaseConnection function
      }),
      plugins: [connectionReleasePlugin],
    });

    const response = await yoga.fetch("http://localhost/graphql", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: "{ hello }",
      }),
    });

    expect(response.status).toBe(200);
    expect(mockReleaseConnection).not.toHaveBeenCalled();
  });
});
