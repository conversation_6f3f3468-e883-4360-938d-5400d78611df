/* eslint-disable no-underscore-dangle */
import assert from "node:assert";
import { GraphQLObjectTypeExtensions } from "graphql";

import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import sql from "../sql.js";
import { FieldSet } from "./_common.js";

const User: graphql.UserResolvers<PostgresGraphQlResolverContext> & {
  __extensions?: GraphQLObjectTypeExtensions;
} = {
  __extensions: {
    preloadBatch: async (
      parents: graphql.User[],
      context: PostgresGraphQlResolverContext,
      fieldSet: FieldSet,
    ): Promise<graphql.User[]> => {
      const rows = await context.connection.query<
        graphql.ResolversParentTypes["User"]
      >(sql`
          SELECT
             ${sql.raw(
               Object.keys({ id: {}, name: {}, ...fieldSet })
                 .filter((field) => ["id", "name"].includes(field))
                 .map((field) => `"${field}"`)
                 .join(", "),
             )}
          FROM (
            SELECT
              graphql_user.id::text as "id",
              graphql_user.name as "name",
              graphql_user.slug as "slug"
            FROM
              graphql_user
            WHERE
              graphql_user.id IN (${sql.raw(
                parents.map((parent) => Number(parent.id)).join(", "),
              )})
          )
        `);

      const rowById = new Map(rows.map((row) => [row.id, row]));

      return parents.map((parent) => {
        const row = rowById.get(parent.id);

        assert(row, `Cannot find User with id "${parent.id}"`);

        return row;
      });
    },
  },
};

export default User;
