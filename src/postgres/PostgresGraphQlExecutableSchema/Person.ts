/* eslint-disable no-underscore-dangle */
import assert from "node:assert";
import { GraphQLObjectTypeExtensions } from "graphql";

import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import sql from "../sql.js";
import { batchResolveNested, FieldSet } from "./_common.js";

const Person: graphql.PersonResolvers<PostgresGraphQlResolverContext> & {
  __extensions?: GraphQLObjectTypeExtensions;
} = {
  __extensions: {
    preloadBatch: async (
      parents: graphql.Person[],
      context: PostgresGraphQlResolverContext,
      fieldSet: FieldSet,
    ): Promise<graphql.Person[]> => {
      const rows = await context.connection.query<
        graphql.ResolversParentTypes["Person"]
      >(sql`
          SELECT
             ${sql.raw(
               Object.keys({ id: {}, fullName: {}, ...fieldSet })
                 .filter((field) =>
                   ["id", "slug", "fullName", "images", "links"].includes(
                     field,
                   ),
                 )
                 .map((field) => `"${field}"`)
                 .join(", "),
             )}
          FROM (
            SELECT
              graphql_person.id::text as "id",
              COALESCE(graphql_person.slug, graphql_person.id::text) as "slug",
              graphql_person.full_name as "fullName",
              COALESCE((
                SELECT
                  JSON_AGG("v".*)
                FROM (
                  SELECT
                    JSON_AGG("t".*) as "sizes"
                  FROM (
                    SELECT
                      graphql_person_image.url as "url",
                      graphql_person_image.width as "width",
                      graphql_person_image.height as "height",
                      graphql_person_image.order as "order"
                    FROM
                      graphql_person_image
                    WHERE
                      graphql_person_image.person_id = graphql_person.id
                    ORDER BY
                      graphql_person_image."order"
                  ) "t"
                  GROUP BY
                    "order"
                ) "v"
              ), '[]'::json) as "images",
              graphql_person.links as "links"
            FROM
              graphql_person
            WHERE
              graphql_person.id IN (${sql.raw(
                parents.map((parent) => Number(parent.id)).join(", "),
              )})
          )
        `);

      const rowById = new Map(rows.map((row) => [row.id, row]));

      return parents.map((parent) => {
        const row = rowById.get(parent.id);

        assert(row, `Cannot find Person with id "${parent.id}"`);

        return row;
      });
    },
  },

  lastName: {
    async resolve(parent) {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      return parent.fullName.split(" ").at(-1)!;
    },
  },

  links: {
    async resolve(parent) {
      return {
        __typename: "PersonLinks",
        wikipediaEn: parent.links.wikipediaEn
          ? `https://en.wikipedia.org/wiki/${encodeURIComponent(
              parent.links.wikipediaEn,
            )}`
          : undefined,
        wikipediaRu: parent.links.wikipediaRu
          ? `https://ru.wikipedia.org/wiki/${encodeURIComponent(
              parent.links.wikipediaRu,
            )}`
          : undefined,
      };
    },
  },

  movies: {
    resolve: batchResolveNested(async (parent, args, context) => {
      const rows = await context.connection.query<{
        movieId: string;
      }>(sql`
            SELECT
              graphql_movie.id::text as "movieId"
            FROM
              graphql_movie_director
            JOIN
              graphql_movie
              ON graphql_movie.id = graphql_movie_director.movie_id
            LEFT JOIN
              graphql_movie_top
              ON graphql_movie_top.movie_id = graphql_movie.id
              AND graphql_movie_top.user_id = ${Number(context.accountId)}
            WHERE
              graphql_movie_director.person_id = (${Number(parent.id)})
            ORDER BY
              ${sql.raw(
                {
                  [graphql.MovieSort
                    .Chronological]: `graphql_movie.year ASC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .ReverseChronological]: `graphql_movie.year DESC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .BestFirst]: `graphql_movie_top."position" ASC, graphql_movie.id DESC`,
                  [graphql.MovieSort
                    .Random]: `graphql_movie_top."position" ASC, graphql_movie.id DESC`,
                }[args.sort],
              )}
            LIMIT
              ${args.limit ?? 100}
        `);

      return rows.map((row) => ({
        id: row.movieId,
      })) as graphql.ResolversParentTypes["Movie"][];
    }),
  },
};

export default Person;
